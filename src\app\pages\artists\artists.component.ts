import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { SectionTitleComponent } from '../../components/shared/section-title/section-title.component';
import { MithilaSectionComponent } from '../../components/shared/mithila-section/mithila-section.component';
import { MithilaArtBackgroundComponent } from '../../components/shared/mithila-art-background/mithila-art-background.component';

import { MithilaDecorativeElementComponent } from '../../components/shared/mithila-decorative-element/mithila-decorative-element.component';
import { CartService, CartItem } from '../../services/cart.service';
import { DataService, Artist, ArtistSpotlight } from '../../services/data.service';
import { ArtistSpotlightCardComponent } from '../../components/shared/artist-spotlight-card/artist-spotlight-card.component';
import { SocialMediaLinksComponent } from '../../components/shared/social-media-links/social-media-links.component';
import { ArtistApplicationFormComponent } from '../../components/shared/artist-application-form/artist-application-form.component';

@Component({
  selector: 'app-artists',
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    SectionTitleComponent,
    MithilaSectionComponent,
    MithilaArtBackgroundComponent,
    MithilaDecorativeElementComponent,
    ArtistApplicationFormComponent,
    ArtistSpotlightCardComponent,
    SocialMediaLinksComponent
  ],
  templateUrl: './artists.component.html',
  styleUrl: './artists.component.css'
})
export class ArtistsComponent implements OnInit {
  isArtistApplicationFormOpen = false;
  featuredArtists: Artist[] = [];
  expandedArtists = new Set<string>();
  spotlightArtist: ArtistSpotlight | null = null;

  constructor(
    private cartService: CartService,
    private dataService: DataService
  ) {}

  ngOnInit() {
    this.featuredArtists = this.dataService.getFeaturedArtists();
    this.spotlightArtist = this.dataService.getArtistSpotlight();
  }

  isArtistExpanded(artistId: string): boolean {
    return this.expandedArtists.has(artistId);
  }

  toggleArtistDescription(artist: Artist) {
    if (this.expandedArtists.has(artist.id)) {
      this.expandedArtists.delete(artist.id);
    } else {
      this.expandedArtists.add(artist.id);
    }
  }
  addToCart(artwork: any, event: Event) {
    event.preventDefault();
    event.stopPropagation();

    const cartItem: Omit<CartItem, 'quantity'> = {
      id: artwork.id,
      name: artwork.name,
      slug: artwork.slug,
      price: artwork.price,
      image: artwork.image,
      artist: artwork.artist
    };

    this.cartService.addToCart(cartItem, 1);
  }

  isInCart(artworkId: string): boolean {
    return this.cartService.isInCart(artworkId);
  }

  formatPrice(price: number): string {
    return `NPR ${price.toLocaleString()}`;
  }

  openArtistApplicationForm() {
    this.isArtistApplicationFormOpen = true;
  }

  closeArtistApplicationForm() {
    this.isArtistApplicationFormOpen = false;
  }

  onArtistApplicationSubmit(formData: any) {
    console.log('Artist application submitted:', formData);
    // Here you would typically send the data to your backend
    this.closeArtistApplicationForm();
  }
}
