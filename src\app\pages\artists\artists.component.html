<!-- <PERSON> Banner -->
<div class="relative h-[50vh] overflow-hidden">
  <!-- Background Image -->
  <div class="absolute inset-0 bg-cover bg-center bg-no-repeat"
    style="background-image: url('https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg')">
  </div>

  <!-- Gradient Overlay -->
  <div class="absolute inset-0 bg-gradient-to-br from-primary-800/90 via-primary-700/90 to-brick-700/90"></div>

  <!-- <PERSON><PERSON><PERSON> Background -->
  <div class="absolute inset-0 opacity-10">
    <app-mithila-art-background [primaryColor]="'#C1440E'" [secondaryColor]="'#F4B400'" opacity="15">
    </app-mithila-art-background>
  </div>

  <!-- Content -->
  <div class="container mx-auto px-4 h-full flex flex-col justify-center items-center text-center relative z-10">
    <!-- Decorative Element -->
    <app-mithila-decorative-element [primaryColor]="'#C1440E'" [secondaryColor]="'#F4B400'" [type]="'lotus'"
      position="relative mb-6" classes="opacity-90" size="80px">
    </app-mithila-decorative-element>

    <!-- Title -->
    <h1 class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-4">
      Our Artists
    </h1>

    <!-- Subtitle -->
    <p class="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
      Meet the talented artists and shop their beautiful creations
    </p>
  </div>
</div>

<!-- Artists Section -->
<app-mithila-section primaryColor="#F4B400" secondaryColor="#264653"
  backgroundGradient="from-secondary-50 via-background-light to-accent-50" backgroundOpacity="15"
  padding="py-16 sm:py-20 md:py-24" [showDecorativeElements]="true">

  <app-section-title title="Our Artists"
    subtitle="Meet our talented artists who specialize in traditional Mithila art forms"></app-section-title>

  <!-- Artist Profile Cards -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
    <div *ngFor="let artist of featuredArtists"
      class="group bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden">

      <!-- Full Width Profile Image -->
      <div class="relative h-64 overflow-hidden">
        <img [src]="artist.image" [alt]="artist.name"
             class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110">

        <!-- Gradient Overlay -->
        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      </div>

      <!-- Artist Details - Half Card Style -->
      <div class="p-6">
        <div class="text-center mb-4">
          <h3 class="text-xl font-bold text-gray-900 mb-1 group-hover:text-primary-600 transition-colors duration-300">{{artist.name}}</h3>
          <p class="text-primary-600 font-medium mb-2">{{artist.role}}</p>
          <div class="text-gray-600 text-sm">
            <p [class.line-clamp-2]="!isArtistExpanded(artist.id)">{{artist.description}}</p>
            <button
              *ngIf="artist.description.length > 100"
              (click)="toggleArtistDescription(artist)"
              class="text-primary-600 hover:text-primary-700 text-xs font-medium mt-1 transition-colors duration-200">
              {{isArtistExpanded(artist.id) ? 'Show Less' : 'Read More'}}
            </button>
          </div>
        </div>

        <!-- Social Media Links -->
        <app-social-media-links
          [socialMedia]="artist.socialMedia"
          size="md"
          layout="horizontal"
          [showLabels]="false">
        </app-social-media-links>
      </div>
    </div>
  </div>
</app-mithila-section>

<!-- Artist Spotlight Section -->
<app-mithila-section primaryColor="#264653" secondaryColor="#3B945E"
  backgroundGradient="from-accent-50 via-background-light to-primary-50" backgroundOpacity="15"
  padding="py-16 sm:py-20 md:py-24" [showDecorativeElements]="true">

  <!-- Artist Spotlight Section -->
  <div *ngIf="spotlightArtist" class="mb-16">
    <app-section-title
      title="Founder Spotlight"
      [subtitle]="'Meet ' + spotlightArtist.name + ', our visionary founder'">
    </app-section-title>

    <div class="mt-12">
      <app-artist-spotlight-card
        [artist]="spotlightArtist"
        [showFullDetails]="true"
        cardStyle="default">
      </app-artist-spotlight-card>
    </div>
  </div>

  <div class="bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 backdrop-blur-md rounded-2xl p-8 border border-primary-500/30 shadow-xl">
    <h3 class="text-2xl font-bold mb-6 text-white">Ready to Join Us?</h3>
    <p class="text-white/95 mb-8 text-lg leading-relaxed">
      We're looking for passionate artists who specialize in traditional Mithila art forms.
      Whether you're experienced or just starting, we welcome artists who are dedicated to preserving and promoting this
      beautiful art form.
    </p>

    <div class="flex flex-wrap justify-center gap-4">
      <button (click)="openArtistApplicationForm()"
        class="bg-white text-primary-600 px-8 py-4 rounded-full font-bold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
        <span class="flex items-center">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
            </path>
          </svg>
          Apply to Join as Artist
        </span>
      </button>
      <a routerLink="/contact"
        class="bg-white/10 backdrop-blur-md text-white px-8 py-4 rounded-full font-bold hover:bg-white/20 transition-all duration-300 transform hover:scale-105 border border-white/30">
        <span class="flex items-center">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z">
            </path>
          </svg>
          Contact Us First
        </span>
      </a>
    </div>
  </div>
</app-mithila-section>

<!-- Artist Application Form Modal -->
<app-artist-application-form [isOpen]="isArtistApplicationFormOpen" (close)="closeArtistApplicationForm()"
  (submit)="onArtistApplicationSubmit($event)">
</app-artist-application-form>