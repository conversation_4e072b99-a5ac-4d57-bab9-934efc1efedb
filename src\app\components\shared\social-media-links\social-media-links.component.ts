import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SocialMediaService, SocialMediaLink } from '../../../services/social-media.service';

@Component({
  selector: 'app-social-media-links',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './social-media-links.component.html',
  styleUrl: './social-media-links.component.css'
})
export class SocialMediaLinksComponent {
  @Input() socialMedia: any;
  @Input() size: 'sm' | 'md' | 'lg' = 'md';
  @Input() layout: 'horizontal' | 'vertical' = 'horizontal';
  @Input() showLabels: boolean = false;

  constructor(public socialMediaService: SocialMediaService) {}

  get socialLinks(): SocialMediaLink[] {
    return this.socialMediaService.generateSocialMediaLinks(this.socialMedia);
  }

  getPlatformConfig(platform: string) {
    return this.socialMediaService.getPlatform(platform);
  }

  getSizeClasses(): string {
    switch (this.size) {
      case 'sm': return 'p-2 h-8 w-8';
      case 'lg': return 'p-4 h-12 w-12';
      default: return 'p-3 h-10 w-10';
    }
  }

  getIconSizeClasses(): string {
    switch (this.size) {
      case 'sm': return 'icon-sm';
      case 'lg': return 'icon-lg';
      default: return 'icon-md';
    }
  }

  getLayoutClasses(): string {
    return this.layout === 'vertical' ? 'flex-col space-y-3' : 'space-x-4';
  }
}
