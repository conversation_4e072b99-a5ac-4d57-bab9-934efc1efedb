<!-- Social Media Links -->
<div *ngIf="socialLinks.length > 0" class="flex justify-center items-center" [ngClass]="getLayoutClasses()">
  <a *ngFor="let link of socialLinks; let i = index"
     [href]="link.url || socialMediaService.getSocialMediaUrl(link.platform, link.username)"
     target="_blank"
     class="group relative"
     [attr.data-tooltip]="'tooltip-' + i">

    <!-- Social Media Button -->
    <div class="rounded-full transition-all duration-300 transform group-hover:scale-110 shadow-lg flex items-center justify-center social-icon"
         [ngClass]="[getSizeClasses(), getPlatformConfig(link.platform)?.color, getPlatformConfig(link.platform)?.hoverColor, getIconSizeClasses()]">
      <div [innerHTML]="getPlatformConfig(link.platform)?.icon"></div>
    </div>

    <!-- Label (if enabled) -->
    <span *ngIf="showLabels"
          class="block mt-2 text-sm text-center"
          [ngClass]="layout === 'vertical' ? 'text-gray-600' : 'text-white/90'">
      {{getPlatformConfig(link.platform)?.name}}
    </span>

    <!-- Tooltip on hover - scoped to this specific link -->
    <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10"
         [id]="'tooltip-' + i">
      {{getPlatformConfig(link.platform)?.name}}
      <div class="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
    </div>
  </a>
</div>

<!-- No social media message -->
<div *ngIf="socialLinks.length === 0" class="text-center text-gray-500 text-sm">
  No social media links available
</div>
